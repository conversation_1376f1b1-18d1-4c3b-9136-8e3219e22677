package model

import "time"

type DeviceStatusLog struct {
	ID             int64     `gorm:"column:id;primary_key"`
	DeviceID       int64     `gorm:"column:device_id"`
	RSSI           int64     `gorm:"column:rssi"`
	BatteryCurrent float64   `gorm:"column:battery_current"`
	BatteryLevel   float64   `gorm:"column:battery_level"`
	BatteryPower   float64   `gorm:"column:battery_power"`
	Lat            float64   `gorm:"column:lat"`
	Long           float64   `gorm:"column:long"`
	CreatedAt      time.Time `gorm:"column:created_at"`
	DeviceTime     time.Time `gorm:"column:device_time"`
}

type DeviceStatusReq struct {
	DeviceCode     string  `json:"device_code"`
	RSSI           int64   `json:"rssi"`
	BatteryCurrent float64 `json:"battery_current"`
	BatteryLevel   float64 `json:"battery_level"`
	BatteryPower   float64 `json:"battery_power"`
	Latitude       float64 `json:"latitude"`
	Longitude      float64 `json:"longitude"`
	Timestamp      int64   `json:"timestamp"`
}
