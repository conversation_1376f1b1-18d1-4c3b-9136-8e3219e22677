package model

import "time"

type Device struct {
	ID              int64     `gorm:"column:id;primary_key"`
	VillageID       int64     `gorm:"column:village_id"`
	Code            string    `gorm:"column:code"`
	Capacity        float64   `gorm:"column:capacity"`
	Status          int16     `gorm:"column:status"`
	Lat             float64   `gorm:"column:lat"`
	Long            float64   `gorm:"column:long"`
	Brand           string    `gorm:"column:brand"`
	Power           int64     `gorm:"column:power"`
	Level           float64   `gorm:"column:level"`
	Type            string    `gorm:"column:type"`
	MacAddress      string    `gorm:"column:mac_address"`
	Sensor          string    `gorm:"column:sensor"`
	IoTInstallDate  time.Time `gorm:"column:iot_install_date"`
	PumpInstallDate time.Time `gorm:"column:pump_install_date"`
	CreatedAt       time.Time `gorm:"column:created_at"`
	UpdatedAt       time.Time `gorm:"column:updated_at"`
}

// DeviceTelemetryReq represents the incoming telemetry data structure
type DeviceTelemetryReq struct {
	ID         string                `json:"id"`
	MacAddress string                `json:"mac_address"`
	Params     DeviceTelemetryParams `json:"params"`
}

// DeviceTelemetryParams contains all telemetry parameter types
type DeviceTelemetryParams struct {
	DevStas DevStasParam `json:"DevStas"`
	CM3     CM3Param     `json:"CM3"`
}

// DevStasParam represents device status parameters
type DevStasParam struct {
	Value string `json:"value"`
	// Parsed values from the comma-separated string
	SamplingInterval string  `json:"-"`
	RSSISignal       int64   `json:"-"`
	BatteryVoltage   float64 `json:"-"`
	ChipTemperature  float64 `json:"-"`
}

// CM3Param represents CM3 counter parameters
type CM3Param struct {
	Value string `json:"value"`
	// Parsed values from the comma-separated string
	AlarmType   int     `json:"-"`
	Timestamp   string  `json:"-"`
	SensorValue float64 `json:"-"`

	ParsedTimestamp time.Time `json:"-"`
}
