package model

import (
	"time"

	"github.com/lib/pq"
)

type Telemetry struct {
	ID              int64           `gorm:"column:id;primary_key"`
	DeviceID        int64           `gorm:"column:device_id"`
	Production      pq.Float64Array `gorm:"column:production;type:_float8[]"`
	Level           float64         `gorm:"column:level"`
	UnixDeviceTime  int64           `gorm:"column:unix_device_time"`
	DeviceTime      time.Time       `gorm:"column:device_time"`
	CreatedAt       time.Time       `gorm:"column:created_at"`
	CalculatedUsage float64         `gorm:"column:calculated_usage"`
}
