package handler

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	MQTT "github.com/eclipse/paho.mqtt.golang"
	"github.com/solarchapter-tech/water-iq-mqtt/model"
	"github.com/solarchapter-tech/water-iq-mqtt/service"
)

type DeviceHandler struct {
	DeviceService service.DeviceService
}

// Hanlder for device status log
func (deviceHandler *DeviceHandler) DeviceStatus(client MQTT.Client, msg MQTT.Message) {
	var req model.DeviceStatusReq
	payload := msg.Payload()
	err := json.Unmarshal(payload, &req)
	if err != nil {
		fmt.Println("err-handler-Unmarshal: ", err)
	}

	err = deviceHandler.DeviceService.DeviceStatus(&req)
	if err != nil {
		fmt.Println("err-service-DeviceStatus: ", err)
	}
}

// Handler for device status and telemetry
func (deviceHandler *<PERSON>ceHandler) DeviceTelemetry(client MQTT.Client, msg MQTT.Message) {
	req, ok := extractMACAddress(msg.Topic())
	if !ok {
		// TODO: will send alert to email
		return
	}
	fmt.Println("mac address: ", req.MacAddress)

	var err error
	req, err = parseTelemetryPayload(msg.Payload(), req)
	if err != nil {
		fmt.Println("err-handler-Unmarshal-telemetry: ", err)
		// TODO: will send alert to email
		return
	}

	req, err = enrichDevStas(req)
	if err != nil {
		fmt.Println("err-handler-enrichDevStas: ", err)
		// TODO: will send alert to email
		return
	}

	req, err = enrichCM3(req)
	if err != nil {
		fmt.Println("err-handler-enrichCM3: ", err)
		// TODO: will send alert to email
		return
	}

	err = deviceHandler.DeviceService.DeviceTelemetry(&req)
	if err != nil {
		fmt.Println("err-service-DeviceTelemetry: ", err)
		// TODO: will send alert to email
		return
	}

	fmt.Printf("Parsed telemetry data: %+v\n", req)
}

// extractMACAddress validates the topic format and returns the MAC address segment.
func extractMACAddress(topic string) (model.DeviceTelemetryReq, bool) {
	var req model.DeviceTelemetryReq
	parts := strings.Split(topic, "/")
	req.MacAddress = parts[len(parts)-1]
	return req, true
}

// parseTelemetryPayload unmarshals the incoming JSON payload.
func parseTelemetryPayload(payload []byte, req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if err := json.Unmarshal(payload, &req); err != nil {
		return req, err
	}
	return req, nil
}

// enrichDevStas parses and enriches DevStas parameters if present.
func enrichDevStas(req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if req.Params.DevStas.Value == "" {
		return req, nil
	}

	parts := strings.Split(req.Params.DevStas.Value, ",")
	if len(parts) < 4 {
		return req, fmt.Errorf("invalid DevStas value")
	}

	rssiSignal, err := strconv.Atoi(parts[1])
	if err != nil {
		return req, err
	}

	batteryVoltage, err := strconv.ParseFloat(parts[2], 64)
	if err != nil {
		return req, err
	}

	chipTemperature, err := strconv.ParseFloat(parts[3], 64)
	if err != nil {
		return req, err
	}

	req.Params.DevStas.SamplingInterval = parts[0]
	req.Params.DevStas.RSSISignal = int64(rssiSignal)
	req.Params.DevStas.BatteryVoltage = batteryVoltage
	req.Params.DevStas.ChipTemperature = chipTemperature

	return req, nil
}

// enrichCM3 parses and enriches CM3 parameters if present.
func enrichCM3(req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if req.Params.CM3.Value == "" {
		return req, nil
	}

	parts := strings.Split(req.Params.CM3.Value, ",")
	if len(parts) < 3 {
		return req, fmt.Errorf("invalid CM3 value")
	}

	alarmType, err := strconv.Atoi(parts[0])
	if err != nil {
		return req, err
	}

	parsedTimestamp, err := time.Parse("2006-01-02 15:04:05", parts[1])
	if err != nil {
		return req, err
	}

	sensorValue, err := strconv.ParseFloat(parts[2], 64)
	if err != nil {
		return req, err
	}

	req.Params.CM3.AlarmType = alarmType
	req.Params.CM3.Timestamp = parts[1]
	req.Params.CM3.SensorValue = sensorValue
	req.Params.CM3.ParsedTimestamp = parsedTimestamp

	return req, nil
}
