package main

import (
	"fmt"
	"os"
	"sync"

	"github.com/solarchapter-tech/water-iq-mqtt/config"
	"github.com/solarchapter-tech/water-iq-mqtt/topic"
)

var wg = sync.WaitGroup{}

func main() {
	wg.Add(1)

	config.DbInit()
	app := topic.App()
	c := config.CreateClient()
	connected()

	token := topic.Route(c, app)
	if token != nil {
		fmt.Println(token.Error())
		os.Exit(1)
	}

	wg.Wait()
}

// prints a short information when client is succesfully connected.
func connected() {
	fmt.Println("=====================================================")
	fmt.Println("* * * HELLO FROM WATERIQ MONITORING SERVER MQTT * * *")
	fmt.Println("=====================================================")
}
