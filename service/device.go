package service

import (
	"fmt"
	"time"

	"github.com/solarchapter-tech/water-iq-mqtt/model"
	"github.com/solarchapter-tech/water-iq-mqtt/repository"
	"github.com/solarchapter-tech/water-iq-mqtt/utils"
)

type DeviceService struct {
	DeviceRepository          repository.DeviceRepository
	DeviceStatusLogRepository repository.DeviceStatusLogRepository
	TelemetryRepository       repository.TelemetryRepository
}

func (c *DeviceService) DeviceStatus(req *model.DeviceStatusReq) error {
	deviceParams := model.Device{Code: req.DeviceCode}
	device, err := c.DeviceRepository.Get(&deviceParams)
	if err != nil {
		return err
	}

	params := model.DeviceStatusLog{
		DeviceID:       device.ID,
		RSSI:           req.RSSI,
		BatteryCurrent: req.BatteryCurrent,
		BatteryLevel:   req.BatteryLevel,
		BatteryPower:   req.BatteryPower,
		Lat:            req.Latitude,
		Long:           req.Longitude,
		CreatedAt:      time.Now(),
		DeviceTime:     time.Unix(req.Timestamp, 0),
	}
	_, err = c.DeviceStatusLogRepository.Insert(&params)
	if err != nil {
		return err
	}

	return nil
}

func (c *DeviceService) DeviceTelemetry(req *model.DeviceTelemetryReq) error {
	deviceParams := model.Device{MacAddress: req.MacAddress}
	device, err := c.DeviceRepository.Get(&deviceParams)
	if err != nil {
		return err
	}
	if device == nil {
		return fmt.Errorf("device not found")
	}

	tx := utils.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	_, err = c.TelemetryRepository.InsertWithTx(tx, model.Telemetry{
		DeviceID:       device.ID,
		Production:     []float64{0},                      // TODO: will be updated later
		Level:          req.Params.CM3.SensorValue * 1000, // convert from m to mm
		UnixDeviceTime: req.Params.CM3.ParsedTimestamp.Unix(),
		DeviceTime:     req.Params.CM3.ParsedTimestamp,
		CreatedAt:      time.Now(),
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	_, err = c.DeviceStatusLogRepository.InsertWithTx(tx, model.DeviceStatusLog{
		DeviceID:       device.ID,
		RSSI:           req.Params.DevStas.RSSISignal,
		BatteryCurrent: req.Params.DevStas.BatteryVoltage,
		BatteryLevel:   0,
		BatteryPower:   0,
		Lat:            0,
		Long:           0,
		CreatedAt:      time.Now(),
		DeviceTime:     req.Params.CM3.ParsedTimestamp,
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
