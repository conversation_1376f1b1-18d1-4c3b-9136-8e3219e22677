package config

import (
	"fmt"

	"github.com/joho/godotenv"
	"github.com/tkanos/gonfig"
)

type Configuration struct {
	MQTTServer   string `env:"MQTT_SERVER"`
	MQTTPort     string `env:"MQTT_PORT"`
	MQTTClientID string `env:"MQTT_CLIENT_ID"`
	MQTTUsername string `env:"MQTT_USERNAME"`
	MQTTPassword string `env:"MQTT_PASSWORD"`

	DbUsername string `env:"DB_USERNAME"`
	DbPassword string `env:"DB_PASSWORD"`
	DbName     string `env:"DB_NAME"`
	DbPort     string `env:"DB_PORT"`
	DbHost     string `env:"DB_HOST"`
}

// Get configuration values from env file
func GetConfig() Configuration {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Error loading .env file:", err)
	}

	configuration := Configuration{}

	err = gonfig.GetConf("", &configuration)
	if err != nil {
		fmt.Println("Error loading config:", err)
	}

	return configuration
}
