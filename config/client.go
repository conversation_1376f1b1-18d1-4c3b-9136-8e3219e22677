package config

import (
	"fmt"

	MQTT "github.com/eclipse/paho.mqtt.golang"
)

// createClient returns a new MQTT client object.
func CreateClient() MQTT.Client {
	cfg := GetConfig()

	mqttServer := fmt.Sprintf("%s:%s", cfg.MQTTServer, cfg.MQTTPort)
	opts := MQTT.NewClientOptions().
		AddBroker(mqttServer).
		SetClientID(cfg.MQTTClientID).
		SetUsername(cfg.MQTTUsername).
		SetPassword(cfg.MQTTPassword)
	opts.AutoReconnect = true

	c := MQTT.NewClient(opts)
	if token := c.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	return c
}
