package repository

import (
	"github.com/jinzhu/gorm"
	"github.com/solarchapter-tech/water-iq-mqtt/model"
)

type TelemetryRepository interface {
	InsertWithTx(tx *gorm.DB, params model.Telemetry) (model.Telemetry, error)
}

type TelemetryRepositoryCtx struct{}

func (c *TelemetryRepositoryCtx) InsertWithTx(tx *gorm.DB, params model.Telemetry) (model.Telemetry, error) {
	err := tx.Create(&params).Error
	if err != nil {
		return model.Telemetry{}, err
	}

	return params, nil
}
