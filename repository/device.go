package repository

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/solarchapter-tech/water-iq-mqtt/config"
	"github.com/solarchapter-tech/water-iq-mqtt/model"
)

type DeviceRepository interface {
	Get(params *model.Device) (*model.Device, error)
}

type DeviceRepositoryCtx struct{}

func (c *DeviceRepositoryCtx) Get(params *model.Device) (*model.Device, error) {
	db := config.DbManager()
	device := model.Device{}

	if params.Code != "" {
		db = db.Where("code = ?", params.Code)
	}

	if params.MacAddress != "" {
		db = db.Where("mac_address = ?", params.MacAddress)
	}

	err := db.First(&device).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &device, nil
}
