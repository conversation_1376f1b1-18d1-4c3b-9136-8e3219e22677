package repository

import (
	"github.com/jinzhu/gorm"
	"github.com/solarchapter-tech/water-iq-mqtt/config"
	"github.com/solarchapter-tech/water-iq-mqtt/model"
)

type DeviceStatusLogRepository interface {
	Insert(params *model.DeviceStatusLog) (*model.DeviceStatusLog, error)
	InsertWithTx(tx *gorm.DB, params model.DeviceStatusLog) (model.DeviceStatusLog, error)
}

type DeviceStatusLogRepositoryCtx struct{}

func (c *DeviceStatusLogRepositoryCtx) Insert(device *model.DeviceStatusLog) (*model.DeviceStatusLog, error) {
	db := config.DbManager()
	err := db.Create(device).Error
	if err != nil {
		return nil, err
	}

	return device, nil
}

func (c *DeviceStatusLogRepositoryCtx) InsertWithTx(tx *gorm.DB, params model.DeviceStatusLog) (model.DeviceStatusLog, error) {
	err := tx.Create(&params).Error
	if err != nil {
		return model.DeviceStatusLog{}, err
	}

	return params, nil
}
