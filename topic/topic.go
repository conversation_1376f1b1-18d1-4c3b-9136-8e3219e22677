package topic

import (
	MQTT "github.com/eclipse/paho.mqtt.golang"
)

// Find all route available and pass it to the handler
func Route(c MQTT.Client, h AppModels) MQTT.Token {
	if token := c.Subscribe("/health-sub", 0, h.Health.HealthCheck); token.Wait() && token.Error() != nil {
		return token
	}

	if token := c.Subscribe("/device-status", 0, h.Device.DeviceStatus); token.Wait() && token.Error() != nil {
		return token
	}

	// find all device
	if token := c.Subscribe("solchap/data/+", 0, h.Device.DeviceTelemetry); token.Wait() && token.Error() != nil {
		return token
	}

	return nil
}
