package topic

import (
	"github.com/solarchapter-tech/water-iq-mqtt/handler"
	"github.com/solarchapter-tech/water-iq-mqtt/repository"
	"github.com/solarchapter-tech/water-iq-mqtt/service"
)

type AppModels struct {
	Health handler.HealthCheckHandler
	Device handler.DeviceHandler
}

func App() AppModels {
	// repository
	deviceRepository := &repository.DeviceRepositoryCtx{}
	deviceStatusLogRepository := &repository.DeviceStatusLogRepositoryCtx{}
	telemetryRepository := &repository.TelemetryRepositoryCtx{}

	// service
	deviceService := service.DeviceService{
		DeviceRepository:          deviceRepository,
		DeviceStatusLogRepository: deviceStatusLogRepository,
		TelemetryRepository:       telemetryRepository,
	}

	// handler
	deviceHandler := handler.DeviceHandler{
		DeviceService: deviceService,
	}
	healthHandler := handler.HealthCheckHandler{}

	return AppModels{
		Health: healthHandler,
		Device: deviceHandler,
	}
}
